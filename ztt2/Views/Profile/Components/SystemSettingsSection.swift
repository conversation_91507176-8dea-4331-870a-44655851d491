//
//  SystemSettingsSection.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 设置项类型枚举
 */
enum SettingType: CaseIterable {
    case iCloudSync
    case productIntroduction
    case feedback
    case about
    case clearAllData
    
    var displayName: String {
        switch self {
        case .iCloudSync:
            return "settings.item.icloud_sync".localized
        case .productIntroduction:
            return "settings.item.product_introduction".localized
        case .feedback:
            return "settings.item.feedback".localized
        case .about:
            return "settings.item.about".localized
        case .clearAllData:
            return "settings.item.clear_all_data".localized
        }
    }
    
    var iconName: String {
        switch self {
        case .iCloudSync:
            return "同步"
        case .productIntroduction:
            return "产品介绍"
        case .feedback:
            return "guzhangfankui"
        case .about:
            return "guanyu"
        case .clearAllData:
            return "shanchu"
        }
    }
    
    var isDestructive: Bool {
        switch self {
        case .clearAllData:
            return true
        default:
            return false
        }
    }
}

/**
 * 系统设置组件
 * 包含6个设置项的列表布局
 */
struct SystemSettingsSection: View {
    
    // MARK: - Properties
    let onSettingItemPressed: (SettingType) -> Void
    
    // MARK: - State
    @State private var sectionAppeared = false
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.md) {
            // 标题
            HStack {
                Text("settings.title".localized)
                    .font(.system(
                        size: DesignSystem.Typography.HeadingMedium.fontSize,
                        weight: DesignSystem.Typography.HeadingMedium.fontWeight
                    ))
                    .foregroundColor(DesignSystem.Colors.profileSettingsTextColor)
                
                Spacer()
            }
            .opacity(sectionAppeared ? 1.0 : 0.0)
            .offset(y: sectionAppeared ? 0 : -10)
            .animation(.easeOut(duration: 0.6).delay(0.1), value: sectionAppeared)
            
            // 设置项列表
            VStack(spacing: DesignSystem.ProfilePage.SettingsItem.spacing) {
                ForEach(SettingType.allCases.indices, id: \.self) { index in
                    let settingType = SettingType.allCases[index]
                    
                    SettingsItemView(
                        settingType: settingType,
                        onTapped: {
                            onSettingItemPressed(settingType)
                        }
                    )
                    .opacity(sectionAppeared ? 1.0 : 0.0)
                    .offset(y: sectionAppeared ? 0 : 20)
                    .animation(.easeOut(duration: 0.6).delay(0.2 + Double(index) * 0.1), value: sectionAppeared)
                }
            }
        }
        .onAppear {
            withAnimation {
                sectionAppeared = true
            }
        }
    }
}

/**
 * 设置项视图
 */
private struct SettingsItemView: View {

    let settingType: SettingType
    let onTapped: () -> Void

    @State private var isPressed = false
    @StateObject private var cloudSyncService = CloudSyncService.shared
    @StateObject private var authManager = AuthenticationManager.shared
    @EnvironmentObject var dataManager: DataManager
    
    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                isPressed = true
                onTapped()
            }
            
            // 恢复按钮状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                isPressed = false
            }
        }) {
            ZStack {
                // 背景容器
                RoundedRectangle(cornerRadius: DesignSystem.ProfilePage.SettingsItem.cornerRadius)
                    .fill(Color(hex: "#f6fbe9"))
                    .frame(height: DesignSystem.ProfilePage.SettingsItem.height)
                    .shadow(color: Color.black.opacity(0.04), radius: 4, x: 0, y: 2)
                    .overlay(
                        RoundedRectangle(cornerRadius: DesignSystem.ProfilePage.SettingsItem.cornerRadius)
                            .stroke(Color.white.opacity(0.8), lineWidth: 1)
                    )
                
                // 内容布局
                HStack(spacing: DesignSystem.ProfilePage.SettingsItem.spacing) {
                    // 左侧图标
                    Image(settingType.iconName)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(
                            width: DesignSystem.ProfilePage.SettingsItem.iconSize,
                            height: DesignSystem.ProfilePage.SettingsItem.iconSize
                        )
                        .foregroundColor(DesignSystem.Colors.profileSettingsIconColor)
                    
                    // 设置项文字
                    Text(settingType.displayName)
                        .font(.system(
                            size: DesignSystem.ProfilePage.SettingsItem.textFont,
                            weight: .medium
                        ))
                        .foregroundColor(Color(hex: "#808080"))
                    
                    Spacer()

                    // 右侧控件（开关或箭头）
                    if settingType == .iCloudSync {
                        // iCloud同步开关
                        VStack(spacing: 4) {
                            Toggle("", isOn: Binding(
                                get: { cloudSyncService.syncEnabled },
                                set: { newValue in
                                    if newValue {
                                        // 检查是否已登录Apple ID
                                        if authManager.isLoggedIn {
                                            cloudSyncService.enableSync()
                                        } else {
                                            // 提示用户登录Apple ID
                                            authManager.requestAppleIDLogin()
                                        }
                                    } else {
                                        cloudSyncService.disableSync()
                                    }
                                }
                            ))
                            .toggleStyle(SwitchToggleStyle(tint: DesignSystem.Colors.primary))
                            .disabled(!canUseiCloudSync())

                            // 状态文字
                            if cloudSyncService.isSyncing {
                                Text("同步中...")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            } else if let error = cloudSyncService.syncError {
                                Text("同步失败")
                                    .font(.caption2)
                                    .foregroundColor(.red)
                            } else if cloudSyncService.syncEnabled {
                                Text("已启用")
                                    .font(.caption2)
                                    .foregroundColor(.green)
                            }
                        }
                    } else {
                        // 普通设置项的箭头
                        Image(systemName: "chevron.right")
                            .font(.system(
                                size: DesignSystem.ProfilePage.SettingsItem.arrowSize,
                                weight: .medium
                            ))
                            .foregroundColor(DesignSystem.Colors.profileSettingsArrowColor)
                    }
                }
                .padding(.horizontal, DesignSystem.ProfilePage.SettingsItem.padding)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
    }

    /**
     * 检查是否可以使用iCloud同步功能
     */
    private func canUseiCloudSync() -> Bool {
        // 检查用户是否为付费用户
        // 这里需要根据实际的订阅状态来判断
        // 暂时返回true，后续可以集成订阅管理器
        return dataManager.currentUser?.isPremiumMember ?? false ||
               dataManager.currentUser?.isBasicMemberOrAbove ?? false
    }
}

// MARK: - Preview
#Preview {
    SystemSettingsSection { settingType in
        print("设置项被点击: \(settingType.displayName)")
    }
    .padding()
    .background(DesignSystem.Colors.background)
}
