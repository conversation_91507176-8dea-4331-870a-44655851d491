//
//  CloudSyncService.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/2.
//

import Foundation
import CloudKit
import CoreData
import Combine

/**
 * iCloud同步服务
 * 负责管理数据的iCloud同步，包括上传、下载和冲突解决
 */
class CloudSyncService: ObservableObject {
    
    // MARK: - Shared Instance
    static let shared = CloudSyncService()
    
    // MARK: - Published Properties
    @Published var isSyncing: Bool = false
    @Published var syncEnabled: Bool = false
    @Published var lastSyncDate: Date?
    @Published var syncError: String?
    
    // MARK: - Private Properties
    private let container: CKContainer
    private let database: CKDatabase
    private let authManager = AuthenticationManager.shared
    private let dataManager = DataManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Constants
    private let recordZoneID = CKRecordZone.ID(zoneName: "FamilyDataZone", ownerName: CKCurrentUserDefaultName)
    
    // MARK: - Initialization
    private init() {
        self.container = CKContainer(identifier: "iCloud.com.rainkygong.ztt2")
        self.database = container.privateCloudDatabase
        
        // 监听认证状态变化
        setupAuthObserver()
        
        // 检查iCloud可用性
        checkiCloudAvailability()
    }
    
    // MARK: - Public Methods
    
    /**
     * 启用iCloud同步
     */
    func enableSync() {
        guard authManager.isLoggedIn else {
            syncError = "需要先登录Apple ID才能使用iCloud同步"
            return
        }
        
        print("🔄 启用iCloud同步")
        syncEnabled = true
        UserDefaults.standard.set(true, forKey: "icloud_sync_enabled")
        
        // 立即执行一次同步
        performSync()
    }
    
    /**
     * 禁用iCloud同步
     */
    func disableSync() {
        print("⏸️ 禁用iCloud同步")
        syncEnabled = false
        UserDefaults.standard.set(false, forKey: "icloud_sync_enabled")
    }
    
    /**
     * 手动执行同步
     */
    func performSync() {
        guard syncEnabled && authManager.isLoggedIn else {
            print("❌ 同步条件不满足：syncEnabled=\(syncEnabled), isLoggedIn=\(authManager.isLoggedIn)")
            return
        }
        
        guard !isSyncing else {
            print("⚠️ 同步正在进行中，跳过本次请求")
            return
        }
        
        print("🚀 开始执行iCloud同步")
        isSyncing = true
        syncError = nil
        
        Task {
            do {
                // 1. 创建自定义Zone（如果不存在）
                try await createCustomZoneIfNeeded()
                
                // 2. 上传本地数据到iCloud
                try await uploadLocalData()
                
                // 3. 下载iCloud数据到本地
                try await downloadCloudData()
                
                await MainActor.run {
                    self.lastSyncDate = Date()
                    self.isSyncing = false
                    print("✅ iCloud同步完成")
                }
                
            } catch {
                await MainActor.run {
                    self.syncError = error.localizedDescription
                    self.isSyncing = false
                    print("❌ iCloud同步失败: \(error)")
                }
            }
        }
    }
    
    /**
     * 检查iCloud账户状态
     */
    func checkiCloudStatus() async -> CKAccountStatus {
        return await container.accountStatus()
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置认证状态观察者
     */
    private func setupAuthObserver() {
        authManager.$isLoggedIn
            .sink { [weak self] isLoggedIn in
                if !isLoggedIn {
                    // 用户退出登录时禁用同步
                    self?.disableSync()
                }
            }
            .store(in: &cancellables)
    }
    
    /**
     * 检查iCloud可用性
     */
    private func checkiCloudAvailability() {
        Task {
            let status = await checkiCloudStatus()
            await MainActor.run {
                switch status {
                case .available:
                    print("✅ iCloud账户可用")
                    // 恢复之前的同步设置
                    self.syncEnabled = UserDefaults.standard.bool(forKey: "icloud_sync_enabled")
                case .noAccount:
                    print("❌ 未登录iCloud账户")
                    self.syncError = "请在设置中登录iCloud账户"
                case .restricted:
                    print("❌ iCloud账户受限")
                    self.syncError = "iCloud账户受限，无法使用同步功能"
                case .couldNotDetermine:
                    print("❓ 无法确定iCloud账户状态")
                    self.syncError = "无法确定iCloud账户状态"
                case .temporarilyUnavailable:
                    print("⚠️ iCloud暂时不可用")
                    self.syncError = "iCloud暂时不可用，请稍后再试"
                @unknown default:
                    print("❓ 未知的iCloud账户状态")
                    self.syncError = "未知的iCloud账户状态"
                }
            }
        }
    }
    
    /**
     * 创建自定义Zone
     */
    private func createCustomZoneIfNeeded() async throws {
        let zone = CKRecordZone(zoneID: recordZoneID)
        
        do {
            _ = try await database.save(zone)
            print("✅ 创建iCloud Zone成功")
        } catch let error as CKError {
            if error.code == .zoneNotFound || error.code == .userDeletedZone {
                // Zone不存在，创建新的
                _ = try await database.save(zone)
                print("✅ 重新创建iCloud Zone成功")
            } else {
                throw error
            }
        }
    }
    
    /**
     * 上传本地数据到iCloud
     */
    private func uploadLocalData() async throws {
        print("📤 开始上传本地数据到iCloud")
        
        // 获取需要同步的数据
        let members = dataManager.members
        var recordsToSave: [CKRecord] = []
        
        // 转换Member数据为CloudKit记录
        for member in members {
            let record = CKRecord(recordType: "Member", zoneID: recordZoneID)
            record["name"] = member.name
            record["role"] = member.role
            record["currentPoints"] = member.currentPoints
            record["memberNumber"] = member.memberNumber
            record["createdAt"] = member.createdAt
            record["updatedAt"] = member.updatedAt
            
            if let birthDate = member.birthDate {
                record["birthDate"] = birthDate
            }
            
            recordsToSave.append(record)
        }
        
        // 批量保存记录
        if !recordsToSave.isEmpty {
            let (savedRecords, _) = try await database.modifyRecords(saving: recordsToSave, deleting: [])
            print("✅ 成功上传 \(savedRecords.count) 条记录到iCloud")
        }
    }
    
    /**
     * 从iCloud下载数据
     */
    private func downloadCloudData() async throws {
        print("📥 开始从iCloud下载数据")
        
        let query = CKQuery(recordType: "Member", predicate: NSPredicate(value: true))
        let (matchResults, _) = try await database.records(matching: query, inZoneWith: recordZoneID)
        
        var downloadedCount = 0
        
        for (_, result) in matchResults {
            switch result {
            case .success(let record):
                // 处理下载的记录
                await processDownloadedRecord(record)
                downloadedCount += 1
            case .failure(let error):
                print("❌ 下载记录失败: \(error)")
            }
        }
        
        print("✅ 成功下载 \(downloadedCount) 条记录从iCloud")
    }
    
    /**
     * 处理下载的记录
     */
    private func processDownloadedRecord(_ record: CKRecord) async {
        await MainActor.run {
            // 这里可以实现更复杂的冲突解决逻辑
            // 目前简单地更新本地数据
            print("📝 处理下载的记录: \(record.recordID)")
            
            // 根据实际需求实现数据合并逻辑
            // 例如：比较时间戳，选择最新的数据等
        }
    }
}
