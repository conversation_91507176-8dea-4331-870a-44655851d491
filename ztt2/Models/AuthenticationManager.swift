//
//  AuthenticationManager.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/2.
//

import Foundation
import AuthenticationServices
import SwiftUI
import CoreData

/**
 * 认证管理器
 * 负责管理Apple ID登录状态，支持静默登录和按需登录
 */
class AuthenticationManager: ObservableObject {
    
    // MARK: - Shared Instance
    static let shared = AuthenticationManager()
    
    // MARK: - Published Properties
    @Published var isLoggedIn: Bool = false
    @Published var currentAppleUserID: String?
    @Published var userName: String?
    @Published var userEmail: String?
    @Published var isLoading: Bool = false
    
    // MARK: - Private Properties
    private let keychainManager = KeychainManager.shared
    
    // MARK: - Initialization
    private init() {
        // 应用启动时检查登录状态
        checkLoginStatus()
    }
    
    // MARK: - Public Methods
    
    /**
     * 检查登录状态（静默检查）
     */
    func checkLoginStatus() {
        print("🔍 开始检查Apple ID登录状态")
        
        // 从Keychain获取保存的Apple用户ID
        guard let savedAppleUserID = keychainManager.getAppleUserID() else {
            print("❌ 未找到保存的Apple用户ID")
            DispatchQueue.main.async {
                self.isLoggedIn = false
                self.isLoading = false
            }
            return
        }
        
        print("✅ 找到保存的Apple用户ID: \(savedAppleUserID)")
        
        // 验证Apple登录状态
        verifyAppleLoginStatus(userID: savedAppleUserID)
    }
    
    /**
     * 请求Apple ID登录（按需登录）
     */
    func requestAppleIDLogin() {
        print("🍎 开始Apple ID登录流程")
        isLoading = true
        
        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]
        
        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.delegate = self
        authorizationController.presentationContextProvider = self
        authorizationController.performRequests()
    }
    
    /**
     * 退出登录
     */
    func logout() {
        print("🚪 用户退出登录")
        
        // 清除Keychain中的登录信息
        keychainManager.clearLoginInfo()
        
        // 更新状态
        DispatchQueue.main.async {
            self.isLoggedIn = false
            self.currentAppleUserID = nil
            self.userName = nil
            self.userEmail = nil
        }
    }
    
    /**
     * 检查是否可以使用iCloud同步功能
     */
    func canUseiCloudSync() -> Bool {
        return isLoggedIn
    }
    
    // MARK: - Private Methods
    
    /**
     * 验证Apple登录状态
     */
    private func verifyAppleLoginStatus(userID: String) {
        let provider = ASAuthorizationAppleIDProvider()
        provider.getCredentialState(forUserID: userID) { [weak self] credentialState, error in
            DispatchQueue.main.async {
                switch credentialState {
                case .authorized:
                    // Apple登录状态有效
                    print("✅ Apple登录状态有效")
                    self?.loadUserInfo(appleUserID: userID)
                case .revoked, .notFound:
                    // Apple登录状态无效，清除本地状态
                    print("❌ Apple登录状态无效或已撤销")
                    self?.clearInvalidLoginState()
                case .transferred:
                    // 用户账号已转移，需要重新登录
                    print("⚠️ Apple账号已转移，需要重新登录")
                    self?.clearInvalidLoginState()
                @unknown default:
                    // 未知状态，清除本地状态
                    print("❓ Apple登录状态未知")
                    self?.clearInvalidLoginState()
                }
                self?.isLoading = false
            }
        }
    }
    
    /**
     * 加载用户信息
     */
    private func loadUserInfo(appleUserID: String) {
        currentAppleUserID = appleUserID
        userName = keychainManager.getUserName()
        userEmail = keychainManager.getUserEmail()
        isLoggedIn = true
        
        print("✅ 用户登录成功: \(userName ?? "未知用户")")
    }
    
    /**
     * 清除无效的登录状态
     */
    private func clearInvalidLoginState() {
        keychainManager.clearLoginInfo()
        isLoggedIn = false
        currentAppleUserID = nil
        userName = nil
        userEmail = nil
    }
    
    /**
     * 处理登录成功
     */
    private func handleSuccessfulLogin(userID: String, fullName: PersonNameComponents?, email: String?) {
        print("🎉 Apple ID登录成功")
        
        // 保存登录信息到Keychain
        let displayName = fullName != nil ? PersonNameComponentsFormatter().string(from: fullName!) : nil
        keychainManager.saveLoginInfo(
            appleUserID: userID,
            userName: displayName,
            userEmail: email
        )
        
        // 更新状态
        DispatchQueue.main.async {
            self.currentAppleUserID = userID
            self.userName = displayName
            self.userEmail = email
            self.isLoggedIn = true
            self.isLoading = false
        }
    }
    
    /**
     * 处理登录失败
     */
    private func handleLoginFailure(_ error: Error) {
        print("❌ Apple ID登录失败: \(error.localizedDescription)")
        
        DispatchQueue.main.async {
            self.isLoading = false
        }
    }
}

// MARK: - ASAuthorizationControllerDelegate
extension AuthenticationManager: ASAuthorizationControllerDelegate {
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        guard let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential else {
            handleLoginFailure(NSError(domain: "AuthError", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的Apple ID凭证"]))
            return
        }
        
        let userID = appleIDCredential.user
        let fullName = appleIDCredential.fullName
        let email = appleIDCredential.email
        
        handleSuccessfulLogin(userID: userID, fullName: fullName, email: email)
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        handleLoginFailure(error)
    }
}

// MARK: - ASAuthorizationControllerPresentationContextProviding
extension AuthenticationManager: ASAuthorizationControllerPresentationContextProviding {
    
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return UIWindow()
        }
        return window
    }
}
