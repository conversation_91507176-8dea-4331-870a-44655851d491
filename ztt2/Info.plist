<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<!-- 应用基本信息 -->
	<key>CFBundleDevelopmentRegion</key>
	<string>zh-<PERSON></string>
	<key>CFBundleDisplayName</key>
	<string>转团团</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	
	<!-- 支持的设备方向 -->
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	
	<!-- iPad支持的设备方向 -->
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	
	<!-- 最低iOS版本 -->
	<key>LSMinimumSystemVersion</key>
	<string>15.6</string>
	
	<!-- 网络安全配置 -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSExceptionDomains</key>
		<dict>
			<!-- DeepSeek API -->
			<key>api.deepseek.com</key>
			<dict>
				<key>NSExceptionRequiresForwardSecrecy</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	
	<!-- 隐私权限说明 -->
	<key>NSUserTrackingUsageDescription</key>
	<string>转团团需要此权限来提供个性化的家庭教育体验和改进应用功能。您的隐私对我们很重要，我们不会收集任何个人身份信息。</string>
	
	<!-- CloudKit配置 -->
	<key>CKSharingSupported</key>
	<true/>
	
	<!-- URL Schemes配置 -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.rainkygong.ztt2.deeplink</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ztt2</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.rainkygong.ztt2.cloudkit</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>icloud-com-rainkygong-ztt2</string>
			</array>
		</dict>
	</array>
	
	<!-- 后台模式 -->
	<key>UIBackgroundModes</key>
	<array>
		<string>background-processing</string>
		<string>remote-notification</string>
	</array>
	
	<!-- 应用特性 -->
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	
	<!-- 支持的文档类型 -->
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>转团团家庭数据</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.rainkygong.ztt2.family-data</string>
			</array>
		</dict>
	</array>
	
	<!-- 导出的UTI类型 -->
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeIdentifier</key>
			<string>com.rainkygong.ztt2.family-data</string>
			<key>UTTypeDescription</key>
			<string>转团团家庭数据</string>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
			</array>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>ztt2</string>
				</array>
			</dict>
		</dict>
	</array>
</dict>
</plist>
